package com.greenterp

import android.view.MotionEvent
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.*
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import kotlinx.coroutines.launch

/**
 * 基于Compose Canvas的画布组件
 * 提供实时绘制功能
 */
@Composable
fun InkCanvas(
    sessionId: String = "default",
    timestamp: String = "",
    isDrawingMode: Boolean = false,
    onDrawingModeChanged: (Boolean) -> Unit = {},
    onLayoutSwapRequested: () -> Unit = {},
    onBackClick: () -> Unit = {},
    onNextClick: () -> Unit = {},
    isDarkTheme: Boolean = false,
    modifier: Modifier = Modifier
) {
    val coroutineScope = rememberCoroutineScope()

    // 画布状态
    var paths by remember(sessionId) { mutableStateOf<List<DrawingPath>>(emptyList()) }
    var currentPath by remember(sessionId) { mutableStateOf(Path()) }
    var currentBrushColor by remember(sessionId) {
        mutableStateOf(if (isDarkTheme) Color.White else Color.Black)
    }
    var currentStrokeWidth by remember(sessionId) { mutableStateOf(5f) }
    var showColorPicker by remember(sessionId) { mutableStateOf(false) }
    var isDrawing by remember(sessionId) { mutableStateOf(false) }
    
    Box(modifier = modifier.fillMaxSize()) {
        // 顶部工具栏
        if (isDrawingMode) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp)
                    .zIndex(1f),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 绘制模式切换按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(
                            if (isDrawingMode) Color(0xFF07c160) else (if (isDarkTheme) Color(0xFF404040) else Color(0xFFF0F0F0))
                        )
                        .clickable {
                            onDrawingModeChanged(!isDrawingMode)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Edit,
                        contentDescription = "Drawing Mode",
                        tint = if (isDrawingMode) Color.White else (if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666)),
                        modifier = Modifier.size(14.dp)
                    )
                }
                
                // 颜色选择按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(currentBrushColor)
                        .clickable { showColorPicker = true }
                )
                
                // 清空画布按钮
                Box(
                    modifier = Modifier
                        .size(22.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(if (isDarkTheme) Color(0xFF404040) else Color(0xFFF0F0F0))
                        .clickable {
                            finishedStrokes = emptyList()
                            inProgressStrokesView?.clear()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Refresh,
                        contentDescription = "Clear Canvas",
                        tint = if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666),
                        modifier = Modifier.size(13.dp)
                    )
                }
            }
        }
        
        // Android Ink画布
        AndroidView(
            factory = { context ->
                InProgressStrokesView(context).apply {
                    setBackgroundColor(if (isDarkTheme) android.graphics.Color.BLACK else android.graphics.Color.WHITE)
                    
                    // 设置完成监听器
                    finishedStrokesListener = InProgressStrokesFinishedListener { strokes ->
                        coroutineScope.launch {
                            finishedStrokes = finishedStrokes + strokes
                        }
                    }
                    
                    // 设置触摸监听器
                    setOnTouchListener { view, event ->
                        if (isDrawingMode) {
                            handleTouchEvent(this, event, brush)
                        } else {
                            false
                        }
                    }
                    
                    inProgressStrokesView = this
                }
            },
            update = { view ->
                // 更新背景色
                view.setBackgroundColor(if (isDarkTheme) android.graphics.Color.BLACK else android.graphics.Color.WHITE)
                
                // 更新触摸监听器
                view.setOnTouchListener { _, event ->
                    if (isDrawingMode) {
                        handleTouchEvent(view, event, brush)
                    } else {
                        false
                    }
                }
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // 颜色选择器弹窗
        if (showColorPicker) {
            com.greenterp.ui.components.ColorPickerDialog(
                currentColor = currentBrushColor,
                onColorSelected = { selectedColor ->
                    currentBrushColor = selectedColor
                },
                onDismiss = { showColorPicker = false },
                isDarkTheme = isDarkTheme,
                title = "Choose Brush Color"
            )
        }
        
        // 可拖动的Back/Next按钮
        DraggableNavigationButtons(
            onBackClick = onBackClick,
            onNextClick = onNextClick,
            isDarkTheme = isDarkTheme,
            modifier = Modifier.fillMaxSize()
        )
    }
}

/**
 * 创建Android Ink画笔
 */
private fun createInkBrush(color: Color, strokeWidth: Float): Brush {
    return Brush.Builder()
        .setColorIntArgb(color.toArgb())
        .setSize(strokeWidth)
        .build()
}

/**
 * 处理触摸事件
 */
private fun handleTouchEvent(
    view: InProgressStrokesView,
    event: MotionEvent,
    brush: Brush
): Boolean {
    return when (event.action) {
        MotionEvent.ACTION_DOWN -> {
            // 开始新的笔画
            try {
                view.startStroke(event, brush)
                true
            } catch (e: Exception) {
                false
            }
        }
        MotionEvent.ACTION_MOVE -> {
            // 继续笔画
            try {
                view.addToStroke(event)
                true
            } catch (e: Exception) {
                false
            }
        }
        MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
            // 结束笔画
            try {
                view.finishStroke(event)
                true
            } catch (e: Exception) {
                false
            }
        }
        else -> false
    }
}
