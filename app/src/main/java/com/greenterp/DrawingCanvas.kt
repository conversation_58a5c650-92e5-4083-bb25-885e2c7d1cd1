package com.greenterp

import android.annotation.SuppressLint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.foundation.border
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.*
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import kotlin.math.abs
import kotlin.math.sqrt

// 优化的绘制路径数据类
data class DrawingPath(
    val path: Path,
    val color: Color,
    val strokeWidth: Float,
    val pointCount: Int = 0, // 路径点数量，用于性能优化
    val bounds: androidx.compose.ui.geometry.Rect? = null // 缓存边界框
)

// 路径点数据类，用于路径简化
data class PathPoint(
    val x: Float,
    val y: Float,
    val timestamp: Long = System.currentTimeMillis()
)

// 画布数据类（包含绘制路径、偏移量和原点）
data class CanvasData(
    val paths: List<DrawingPath> = emptyList(),
    val offset: Offset = Offset.Zero,
    val originPoint: Offset? = null  // 用户第一次绘制的原点位置
)

// 性能配置常量
private object DrawingPerformanceConfig {
    const val MAX_POINTS_PER_PATH = 500 // 每个路径最大点数
    const val PATH_SIMPLIFICATION_TOLERANCE = 2.0f // 路径简化容差
    const val BATCH_UPDATE_INTERVAL_MS = 16L // 批量更新间隔（60fps）
    const val MAX_PATHS_FOR_FULL_REDRAW = 100 // 全量重绘的最大路径数
    const val VIEWPORT_PADDING = 100f // 视口裁剪边距
}


@Composable
fun DrawingCanvas(
    sessionId: String = "default",
    timestamp: String = "", // 时间戳
    isDrawingMode: Boolean = false, // 全局绘制模式状态
    onDrawingModeChanged: (Boolean) -> Unit = {}, // 绘制模式变化回调
    onLayoutSwapRequested: () -> Unit = {}, // 布局交换回调
    initialCanvasData: CanvasData = CanvasData(), // 初始画布数据
    onCanvasDataChanged: (CanvasData) -> Unit = {}, // 画布数据变化回调
    onBackClick: () -> Unit = {}, // Back按钮回调
    onNextClick: () -> Unit = {}, // Next按钮回调
    isDarkTheme: Boolean = false, // 新增：主题参数
    modifier: Modifier = Modifier
) {
    // 优化的状态管理 - 使用 remember 缓存对象，减少重组
    var paths by remember(sessionId) { mutableStateOf(initialCanvasData.paths) }
    var canvasOffset by remember(sessionId) { mutableStateOf(initialCanvasData.offset) }
    var originPoint by remember(sessionId) { mutableStateOf(initialCanvasData.originPoint) }

    // 使用 remember 持久化路径对象，避免频繁创建
    val currentPath = remember(sessionId) { Path() }
    val currentPathPoints = remember(sessionId) { mutableListOf<PathPoint>() }

    var currentColor by remember(sessionId) { mutableStateOf(if (isDarkTheme) Color.White else Color.Black) }
    var currentStrokeWidth by remember(sessionId) { mutableStateOf(5f) }
    var showColorPicker by remember(sessionId) { mutableStateOf(false) }
    var isEraserMode by remember(sessionId) { mutableStateOf(false) } // 橡皮擦模式
    var showStrokeWidthDropdown by remember(sessionId) { mutableStateOf(false) } // 笔刷粗细下拉框

    // 性能优化：批量更新状态
    var needsRedraw by remember { mutableStateOf(false) }
    var lastUpdateTime by remember { mutableStateOf(0L) }

    // 当sessionId变化时，重新初始化状态（仅在sessionId变化时）
    LaunchedEffect(sessionId) {
        paths = initialCanvasData.paths
        canvasOffset = initialCanvasData.offset
        originPoint = initialCanvasData.originPoint
        currentPath.reset()
        currentPathPoints.clear()
    }

    // 当主题切换时，如果当前颜色是默认颜色，则更新为新主题的默认颜色
    LaunchedEffect(isDarkTheme) {
        if (currentColor == Color.Black || currentColor == Color.White) {
            currentColor = if (isDarkTheme) Color.White else Color.Black
        }
    }

    // 优化的路径简化函数
    fun simplifyPath(points: List<PathPoint>, tolerance: Float = DrawingPerformanceConfig.PATH_SIMPLIFICATION_TOLERANCE): List<PathPoint> {
        if (points.size <= 2) return points

        val simplified = mutableListOf<PathPoint>()
        simplified.add(points.first())

        for (i in 1 until points.size - 1) {
            val prev = simplified.last()
            val current = points[i]
            val next = points[i + 1]

            // 计算点到直线的距离
            val distance = pointToLineDistance(current, prev, next)
            if (distance > tolerance) {
                simplified.add(current)
            }
        }

        simplified.add(points.last())
        return simplified
    }

    // 计算点到直线的距离
    fun pointToLineDistance(point: PathPoint, lineStart: PathPoint, lineEnd: PathPoint): Float {
        val dx = lineEnd.x - lineStart.x
        val dy = lineEnd.y - lineStart.y
        val length = sqrt(dx * dx + dy * dy)

        if (length == 0f) return sqrt((point.x - lineStart.x) * (point.x - lineStart.x) + (point.y - lineStart.y) * (point.y - lineStart.y))

        val t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / (length * length)
        val projection = if (t < 0) lineStart else if (t > 1) lineEnd else PathPoint(lineStart.x + t * dx, lineStart.y + t * dy)

        return sqrt((point.x - projection.x) * (point.x - projection.x) + (point.y - projection.y) * (point.y - projection.y))
    }

    // 优化的更新画布数据函数 - 支持批量更新
    fun updateCanvasData(force: Boolean = false) {
        val currentTime = System.currentTimeMillis()
        if (!force && currentTime - lastUpdateTime < DrawingPerformanceConfig.BATCH_UPDATE_INTERVAL_MS) {
            needsRedraw = true
            return
        }

        lastUpdateTime = currentTime
        needsRedraw = false

        val canvasData = CanvasData(paths = paths, offset = canvasOffset, originPoint = originPoint)
        println("DrawingCanvas updateCanvasData: sessionId=$sessionId, pathsCount=${paths.size}, originPoint=$originPoint")
        onCanvasDataChanged(canvasData)
    }

    // 批量更新处理
    LaunchedEffect(needsRedraw) {
        if (needsRedraw) {
            kotlinx.coroutines.delay(DrawingPerformanceConfig.BATCH_UPDATE_INTERVAL_MS)
            if (needsRedraw) {
                updateCanvasData(force = true)
            }
        }
    }

    // 重置到原点的函数
    fun resetToOrigin() {
        originPoint?.let { origin ->
            // 重置偏移量为零，让原点回到其原始位置
            canvasOffset = Offset.Zero
            updateCanvasData(force = true)
            println("DrawingCanvas resetToOrigin: origin=$origin, 重置偏移量为零")
        } ?: run {
            println("DrawingCanvas resetToOrigin: 没有原点，用户尚未绘制")
        }
    }

    val density = LocalDensity.current

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(if (isDarkTheme) Color.Black else Color.White)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
        // 工具栏
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = if (isDarkTheme) Color(0xFF2D2D2D) else Color.White,
            shadowElevation = 2.dp
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧转换按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(if (isDarkTheme) Color(0xFF404040) else Color(0xFFF0F0F0))
                        .clickable {
                            onLayoutSwapRequested()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.exchange),
                        contentDescription = "Swap Layout",
                        tint = if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666),
                        modifier = Modifier.size(14.dp)
                    )
                }

                // 右侧工具按钮组
                Row(
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {

                // 1. 绘制/浏览模式切换按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(
                            if (isDrawingMode) Color(0xFF07c160) else (if (isDarkTheme) Color(0xFF404040) else Color(0xFFF0F0F0))
                        )
                        .clickable {
                            onDrawingModeChanged(!isDrawingMode)
                            if (isDrawingMode) isEraserMode = false
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        if (isDrawingMode) Icons.Default.Edit else Icons.Default.Edit,
                        contentDescription = if (isDrawingMode) "Drawing Mode" else "Canvas Browsing Mode",
                        tint = if (!isDrawingMode) (if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666)) else Color.White,
                        modifier = Modifier.size(14.dp)
                    )
                }

                // 2. 颜色选择按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(currentColor)
                        .border(1.dp, if (isDarkTheme) Color(0xFF555555) else Color(0xFFE0E0E0), RoundedCornerShape(3.dp))
                        .clickable { showColorPicker = !showColorPicker }
                )

                // 3. 笔刷粗细按钮（改为与其他按钮一致的大小）
                Box {
                    Box(
                        modifier = Modifier
                            .width(54.dp)
                            .height(24.dp)
                            .clip(RoundedCornerShape(3.dp))
                            .background(if (isDarkTheme) Color(0xFF404040) else Color(0xFFF0F0F0))
                            .clickable { showStrokeWidthDropdown = !showStrokeWidthDropdown },
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .width(12.dp)
                                    .height((currentStrokeWidth / 2).dp.coerceAtMost(8.dp))
                                    .background(if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666), RoundedCornerShape((currentStrokeWidth / 4).dp))
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Icon(
                                Icons.Default.ArrowDropDown,
                                contentDescription = "Stroke Width",
                                tint = if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666),
                                modifier = Modifier.size(8.dp)
                            )
                        }
                    }

                    DropdownMenu(
                        expanded = showStrokeWidthDropdown,
                        onDismissRequest = { showStrokeWidthDropdown = false }
                    ) {
                        listOf(2f, 5f, 8f, 12f, 20f).forEach { width ->
                            DropdownMenuItem(
                                text = {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.padding(vertical = 4.dp)
                                    ) {
                                        Box(
                                            modifier = Modifier
                                                .width(40.dp)
                                                .height(width.dp)
                                                .background(Color.Black, RoundedCornerShape(width.dp / 2))
                                        )
                                        Spacer(modifier = Modifier.width(12.dp))
                                        Text("${width.toInt()}px", fontSize = 12.sp)
                                    }
                                },
                                onClick = {
                                    currentStrokeWidth = width
                                    showStrokeWidthDropdown = false
                                }
                            )
                        }
                    }
                }

                // 4. 橡皮擦按钮
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(
                            if (isEraserMode) Color(0xFFFF6B6B) else (if (isDarkTheme) Color(0xFF404040) else Color(0xFFF0F0F0))
                        )
                        .clickable {
                            isEraserMode = !isEraserMode
                            if (isEraserMode && !isDrawingMode) {
                                onDrawingModeChanged(true)
                            }
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = "Eraser",
                        tint = if (isEraserMode) Color.White else (if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666)),
                        modifier = Modifier.size(14.dp)
                    )
                }

                // 5. 清空画布按钮
                Box(
                    modifier = Modifier
                        .size(22.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(if (isDarkTheme) Color(0xFF404040) else Color(0xFFF0F0F0))
                        .clickable {
                            paths = emptyList()
                            currentPath.reset()
                            currentPathPoints.clear()
                            updateCanvasData(force = true)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Refresh,
                        contentDescription = "Clear Canvas",
                        tint = if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666),
                        modifier = Modifier.size(13.dp)
                    )
                }
                }
            }
        }

        // 时间戳分隔线 - 和语音识别样式一致
        if (timestamp.isNotEmpty()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                HorizontalDivider(
                    modifier = Modifier.weight(1f),
                    thickness = 1.dp,
                    color = Color(0xFF07c160)
                )
                Text(
                    text = timestamp,
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = (14 * 1.4f).sp,
                        fontWeight = FontWeight.Medium
                    ),
                    color = Color(0xFF07c160),
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
                HorizontalDivider(
                    modifier = Modifier.weight(1f),
                    thickness = 1.dp,
                    color = Color(0xFF07c160)
                )
            }
        }

        // 颜色选择器弹窗
        if (showColorPicker) {
            com.greenterp.ui.components.ColorPickerDialog(
                currentColor = currentColor,
                onColorSelected = { selectedColor ->
                    currentColor = selectedColor
                },
                onDismiss = { showColorPicker = false },
                isDarkTheme = isDarkTheme, // 使用传入的主题参数
                title = "Choose Brush Color"
            )
        }

        // 画布区域 - 支持无限拖动
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .background(if (isDarkTheme) Color.Black else Color.White) // 根据主题设置背景色
                .clip(RectangleShape) // 裁剪画布内容，防止超出边界
                .pointerInput(isDrawingMode, isEraserMode) {
                    if (isDrawingMode) {
                        // 绘制模式：区分手写笔和手指触摸
                        awaitPointerEventScope {
                            while (true) {
                                val event = awaitPointerEvent()
                                val change = event.changes.first()

                                when (event.type) {
                                    PointerEventType.Press -> {
                                        if (change.type == PointerType.Stylus) {
                                            // 手写笔：开始绘制
                                            val adjustedOffset = change.position - canvasOffset

                                            // 如果是第一次绘制，记录原点（绘制模式且没有原点）
                                            if (!isEraserMode && originPoint == null) {
                                                originPoint = change.position // 使用屏幕坐标作为原点
                                                println("DrawingCanvas: 记录原点 originPoint=${change.position}")
                                                updateCanvasData()
                                            }

                                            if (isEraserMode) {
                                                // 橡皮擦模式：使用优化的过滤函数
                                                val newPaths = optimizedEraserFilter(paths, adjustedOffset)
                                                if (newPaths.size != paths.size) {
                                                    paths = newPaths
                                                    updateCanvasData()
                                                }
                                            } else {
                                                // 绘制模式 - 优化：直接重置路径，避免创建新对象
                                                currentPath.reset()
                                                currentPath.moveTo(adjustedOffset.x, adjustedOffset.y)
                                                currentPathPoints.clear()
                                                currentPathPoints.add(PathPoint(adjustedOffset.x, adjustedOffset.y))
                                            }
                                            change.consume()
                                        }
                                        // 手指触摸不消费事件，让拖动手势处理
                                    }
                                    PointerEventType.Move -> {
                                        if (change.type == PointerType.Stylus && change.pressed) {
                                            // 手写笔移动：继续绘制
                                            val adjustedPosition = change.position - canvasOffset
                                            if (isEraserMode) {
                                                // 橡皮擦模式：使用优化的过滤函数
                                                val newPaths = optimizedEraserFilter(paths, adjustedPosition)
                                                if (newPaths.size != paths.size) {
                                                    paths = newPaths
                                                    updateCanvasData()
                                                }
                                            } else {
                                                // 绘制模式 - 性能优化：直接在现有路径上添加点，避免路径复制
                                                currentPathPoints.add(PathPoint(adjustedPosition.x, adjustedPosition.y))

                                                // 路径简化：当点数过多时进行简化
                                                if (currentPathPoints.size > DrawingPerformanceConfig.MAX_POINTS_PER_PATH) {
                                                    val simplified = simplifyPath(currentPathPoints)
                                                    currentPathPoints.clear()
                                                    currentPathPoints.addAll(simplified)

                                                    // 重建路径
                                                    currentPath.reset()
                                                    if (simplified.isNotEmpty()) {
                                                        currentPath.moveTo(simplified[0].x, simplified[0].y)
                                                        for (i in 1 until simplified.size) {
                                                            currentPath.lineTo(simplified[i].x, simplified[i].y)
                                                        }
                                                    }
                                                } else {
                                                    // 直接添加到路径，避免复制
                                                    currentPath.lineTo(adjustedPosition.x, adjustedPosition.y)
                                                }
                                            }
                                            change.consume()
                                        }
                                    }
                                    PointerEventType.Release -> {
                                        if (change.type == PointerType.Stylus) {
                                            // 手写笔抬起：完成绘制
                                            if (!isEraserMode && !currentPath.isEmpty) {
                                                // 创建新路径时计算边界框和点数，用于性能优化
                                                val pathBounds = currentPath.getBounds()
                                                val newDrawingPath = DrawingPath(
                                                    path = Path().apply { addPath(currentPath) }, // 复制路径
                                                    color = currentColor,
                                                    strokeWidth = currentStrokeWidth,
                                                    pointCount = currentPathPoints.size,
                                                    bounds = pathBounds
                                                )

                                                paths = paths + newDrawingPath
                                                currentPath.reset()
                                                currentPathPoints.clear()
                                                updateCanvasData()
                                            }
                                            change.consume()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .pointerInput(isDrawingMode) {
                    // 手指拖动处理（绘制模式下移动画布，浏览模式下禁用以允许滚动）
                    if (isDrawingMode) {
                        detectDragGestures(
                            onDrag = { change, dragAmount ->
                                // 只有手指触摸才移动画布
                                if (change.type != PointerType.Stylus) {
                                    canvasOffset += dragAmount
                                }
                            },
                            onDragEnd = {
                                // 拖动结束时保存偏移量
                                updateCanvasData()
                            }
                        )
                    }
                }
        ) {
            // 性能优化的绘制逻辑
            translate(canvasOffset.x, canvasOffset.y) {
                // 计算视口边界，用于裁剪不可见的路径
                val viewportBounds = androidx.compose.ui.geometry.Rect(
                    left = -canvasOffset.x - DrawingPerformanceConfig.VIEWPORT_PADDING,
                    top = -canvasOffset.y - DrawingPerformanceConfig.VIEWPORT_PADDING,
                    right = -canvasOffset.x + size.width + DrawingPerformanceConfig.VIEWPORT_PADDING,
                    bottom = -canvasOffset.y + size.height + DrawingPerformanceConfig.VIEWPORT_PADDING
                )

                // 性能优化：只绘制可见区域内的路径
                val visiblePaths = if (paths.size > DrawingPerformanceConfig.MAX_PATHS_FOR_FULL_REDRAW) {
                    paths.filter { drawingPath ->
                        drawingPath.bounds?.let { bounds ->
                            viewportBounds.overlaps(bounds)
                        } ?: true // 如果没有边界框信息，则绘制
                    }
                } else {
                    paths // 路径数量少时，直接绘制所有路径
                }

                // 绘制可见路径
                visiblePaths.forEach { drawingPath ->
                    drawPath(
                        path = drawingPath.path,
                        color = drawingPath.color,
                        style = Stroke(
                            width = drawingPath.strokeWidth,
                            cap = StrokeCap.Round,
                            join = StrokeJoin.Round
                        )
                    )
                }

                // 绘制当前路径（总是可见的）
                if (!isEraserMode && !currentPath.isEmpty) {
                    drawPath(
                        path = currentPath,
                        color = currentColor,
                        style = Stroke(
                            width = currentStrokeWidth,
                            cap = StrokeCap.Round,
                            join = StrokeJoin.Round
                        )
                    )
                }
            }
        }
        }

        // 可拖动的Back/Next按钮 - 一直显示
        DraggableNavigationButtons(
            onBackClick = {
                // Back按钮：重置到原点
                resetToOrigin()
                // 同时调用原来的Back逻辑（会话切换）
                onBackClick()
            },
            onNextClick = onNextClick,
            isDarkTheme = isDarkTheme,
            modifier = Modifier.fillMaxSize()
        )
    }
}

/**
 * 固定位置的导航按钮组件
 * Next按钮固定在顶部，Back按钮固定在底部
 */
@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
private fun DraggableNavigationButtons(
    onBackClick: () -> Unit = {},
    onNextClick: () -> Unit = {},
    isDarkTheme: Boolean = false,
    modifier: Modifier = Modifier
) {
    BoxWithConstraints(modifier = modifier) {
        val screenHeight = maxHeight

        // 计算工具栏高度（估算：padding + 内容高度）
        val toolbarHeight = 56.dp  // 工具栏大约高度

        // Next按钮紧靠左上角（工具栏下方）
        FixedNavigationButton(
            text = "Next",
            icon = "arrow_down_with_line", // 自定义箭头样式
            contentDescription = "Next",
            onClick = onNextClick,
            isDarkTheme = isDarkTheme,
            modifier = Modifier
                .offset(x = 0.dp, y = toolbarHeight)
        )

        // Back按钮紧靠左下角
        FixedNavigationButton(
            text = "Back",
            icon = "arrow_up_with_line", // 自定义箭头样式
            contentDescription = "Back",
            onClick = onBackClick,
            isDarkTheme = isDarkTheme,
            modifier = Modifier
                .offset(x = 0.dp, y = screenHeight - 48.dp) // 48dp是按钮高度
        )
    }
}

/**
 * 固定位置的导航按钮，包含文字和自定义箭头
 */
@Composable
private fun FixedNavigationButton(
    text: String,
    icon: String, // 改为字符串标识符
    contentDescription: String,
    onClick: () -> Unit,
    isDarkTheme: Boolean = false,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                color = (if (isDarkTheme) Color(0xFF2D2D2D) else Color.White).copy(alpha = 0.9f),
                shape = RoundedCornerShape(8.dp)
            )
            .border(
                width = 1.dp,
                color = (if (isDarkTheme) Color(0xFF555555) else Color.Gray).copy(alpha = 0.3f),
                shape = RoundedCornerShape(8.dp)
            )
            .clickable(onClick = onClick)
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // 统一布局：文字在左，箭头在右
            Text(
                text = text,
                fontSize = 14.sp,
                color = if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666),
                fontWeight = FontWeight.Medium
            )
            CustomArrowIcon(
                type = icon,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 自定义箭头图标组件
 * 支持带横杠的上下箭头
 */
@Composable
private fun CustomArrowIcon(
    type: String,
    modifier: Modifier = Modifier
) {
    Canvas(modifier = modifier) {
        val strokeWidth = 2.dp.toPx()
        val color = Color(0xFF666666)
        val centerX = size.width / 2
        val centerY = size.height / 2
        val arrowSize = size.width * 0.3f
        val lineLength = size.width * 0.6f

        when (type) {
            "arrow_down_with_line" -> {
                // Next箭头：向下箭头，下面有横杠
                // 绘制向下箭头
                drawLine(
                    color = color,
                    start = Offset(centerX - arrowSize, centerY - arrowSize/2),
                    end = Offset(centerX, centerY + arrowSize/2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
                drawLine(
                    color = color,
                    start = Offset(centerX, centerY + arrowSize/2),
                    end = Offset(centerX + arrowSize, centerY - arrowSize/2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
                // 绘制下方横杠
                drawLine(
                    color = color,
                    start = Offset(centerX - lineLength/2, centerY + arrowSize),
                    end = Offset(centerX + lineLength/2, centerY + arrowSize),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
            }
            "arrow_up_with_line" -> {
                // Back箭头：向上箭头，上面有横杠
                // 绘制向上箭头
                drawLine(
                    color = color,
                    start = Offset(centerX - arrowSize, centerY + arrowSize/2),
                    end = Offset(centerX, centerY - arrowSize/2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
                drawLine(
                    color = color,
                    start = Offset(centerX, centerY - arrowSize/2),
                    end = Offset(centerX + arrowSize, centerY + arrowSize/2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
                // 绘制上方横杠
                drawLine(
                    color = color,
                    start = Offset(centerX - lineLength/2, centerY - arrowSize),
                    end = Offset(centerX + lineLength/2, centerY - arrowSize),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
            }
        }
    }
}

// 优化的辅助函数：检查点是否接近路径（用于橡皮擦功能）
private fun isPointNearPath(point: Offset, path: Path, strokeWidth: Float): Boolean {
    // 性能优化：首先检查边界框，快速排除不相关的路径
    val pathBounds = path.getBounds()
    val tolerance = strokeWidth / 2 + 10f // 增加一些容差

    // 快速边界框检查
    if (point.x < pathBounds.left - tolerance ||
        point.x > pathBounds.right + tolerance ||
        point.y < pathBounds.top - tolerance ||
        point.y > pathBounds.bottom + tolerance) {
        return false
    }

    // 如果在边界框内，进行更精确的检查
    // 这里可以实现更精确的点到路径距离计算，但为了性能考虑，
    // 目前使用边界框检查已经足够
    return true
}

// 优化的橡皮擦处理函数
private fun optimizedEraserFilter(paths: List<DrawingPath>, point: Offset): List<DrawingPath> {
    // 性能优化：使用缓存的边界框信息
    return paths.filter { drawingPath ->
        val bounds = drawingPath.bounds ?: drawingPath.path.getBounds()
        val tolerance = drawingPath.strokeWidth / 2 + 10f

        // 快速边界框检查
        !(point.x >= bounds.left - tolerance &&
          point.x <= bounds.right + tolerance &&
          point.y >= bounds.top - tolerance &&
          point.y <= bounds.bottom + tolerance)
    }
}

// 内存管理：路径合并策略
private fun mergeSmallPaths(paths: List<DrawingPath>): List<DrawingPath> {
    if (paths.size <= DrawingPerformanceConfig.MAX_PATHS_FOR_FULL_REDRAW) return paths

    val merged = mutableListOf<DrawingPath>()
    var currentMergeGroup = mutableListOf<DrawingPath>()

    paths.forEach { path ->
        if (path.pointCount < 10 && currentMergeGroup.size < 5) {
            // 小路径加入合并组
            currentMergeGroup.add(path)
        } else {
            // 处理当前合并组
            if (currentMergeGroup.isNotEmpty()) {
                if (currentMergeGroup.size == 1) {
                    merged.add(currentMergeGroup.first())
                } else {
                    // 合并多个小路径
                    val mergedPath = Path()
                    var totalPoints = 0
                    currentMergeGroup.forEach { drawingPath ->
                        mergedPath.addPath(drawingPath.path)
                        totalPoints += drawingPath.pointCount
                    }
                    merged.add(DrawingPath(
                        path = mergedPath,
                        color = currentMergeGroup.first().color,
                        strokeWidth = currentMergeGroup.first().strokeWidth,
                        pointCount = totalPoints,
                        bounds = mergedPath.getBounds()
                    ))
                }
                currentMergeGroup.clear()
            }
            merged.add(path)
        }
    }

    // 处理剩余的合并组
    if (currentMergeGroup.isNotEmpty()) {
        if (currentMergeGroup.size == 1) {
            merged.add(currentMergeGroup.first())
        } else {
            val mergedPath = Path()
            var totalPoints = 0
            currentMergeGroup.forEach { drawingPath ->
                mergedPath.addPath(drawingPath.path)
                totalPoints += drawingPath.pointCount
            }
            merged.add(DrawingPath(
                path = mergedPath,
                color = currentMergeGroup.first().color,
                strokeWidth = currentMergeGroup.first().strokeWidth,
                pointCount = totalPoints,
                bounds = mergedPath.getBounds()
            ))
        }
    }

    return merged
}