package com.greenterp

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.compose.ui.window.Dialog
import com.greenterp.data.VoiceSession
import com.greenterp.data.RecognitionResult
import com.greenterp.data.DrawingSession
import com.greenterp.data.TranslationSession
import com.greenterp.data.TranslationResult as DataTranslationResult
import com.greenterp.ui.components.ModernSearchBox
import com.greenterp.ui.components.TranslationPopover
import com.greenterp.ui.components.BottomActionBar
import com.greenterp.ui.components.TopBar
import com.greenterp.ui.voice.VoiceRecognitionDisplay
import com.greenterp.ui.voice.VoiceSessionItem
import com.greenterp.ui.translation.SplitVoiceAndTranslateDisplay
import com.greenterp.ui.translation.TranslationArea
import com.greenterp.ui.drawing.DrawingCanvasDisplay
import com.greenterp.ui.drawing.DrawingCanvasItem
import com.greenterp.ui.settings.SettingsDialog
import com.greenterp.ui.vocabulary.VocabularyListDialog
import com.greenterp.ui.replace.ReplaceSceneListDialog
import com.greenterp.network.ApiService
import com.greenterp.network.TranslateSearchRequest
import com.greenterp.network.TranslateSearchResult
import com.greenterp.network.TranslationResult
import com.greenterp.network.TranslationModel
import com.greenterp.network.TranslationModelListResult
import com.greenterp.utils.SettingsManager
import com.greenterp.ui.theme.TerpMetaAndroidTheme
import com.greenterp.ThreeLanguageConfig
import com.greenterp.LanguageType
import com.greenterp.data.GlossaryItem
import com.greenterp.ReplaceManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.async
import java.text.SimpleDateFormat
import java.util.*



/**
 * 检测文本语言并返回翻译语言对
 * 参考JS算法实现中英文检测
 */
private fun detectLanguageAndGetTranslationPair(
    text: String,
    languageList: List<LanguageItem>
): Pair<String, String> {
    // 匹配中文字符的正则表达式
    val chineseRegex = Regex("[\\u4e00-\\u9fff]")
    // 匹配英文字符的正则表达式
    val englishRegex = Regex("[a-zA-Z]")

    val chineseMatches = chineseRegex.findAll(text).count()
    val englishMatches = englishRegex.findAll(text).count()

    val detectedLanguage = when {
        chineseMatches > 0 && englishMatches == 0 -> "zh-CN" // 纯中文
        englishMatches > 0 && chineseMatches == 0 -> "en-US" // 纯英文
        chineseMatches > 0 && englishMatches > 0 -> {
            // 中英文混合，判断哪个多
            if (chineseMatches > englishMatches) {
                "zh-CN" // 中文占主导
            } else if (englishMatches > chineseMatches) {
                "en-US" // 英文占主导
            } else {
                "zh-CN" // 数量相等时默认中文
            }
        }
        else -> "zh-CN" // 无法识别或其他语言，默认中文
    }

    // 找到检测到的源语言
    val sourceLang = languageList.find { it.code == detectedLanguage }
        ?: languageList.find { it.code == "zh-CN" } // 默认中文

    // 找到目标语言（非源语言且非Auto的第一个语言）
    val targetLang = languageList.find { it.code != detectedLanguage && it.code != "Auto" }
        ?: languageList.find { it.code == "en-US" } // 默认英文

    println("讯飞ASR语言检测 - 中文字符: $chineseMatches, 英文字符: $englishMatches, 检测结果: $detectedLanguage")

    return Pair(sourceLang?.code ?: "zh-CN", targetLang?.code ?: "en-US")
}

/**
 * 执行翻译的挂起函数
 */
private suspend fun performTranslation(
    text: String,
    model: TranslationModel,
    fromLanguage: String,
    toLanguage: String
): String {
    val apiService = ApiService.getInstance()

    // 构建翻译请求 - 直接使用语言代码，无需转换
    val translateRequest = TranslateSearchRequest(
        text = text,
        source_lang = fromLanguage,  // 现在直接传入语言代码
        target_lang = toLanguage,    // 现在直接传入语言代码
        translateApi = model
    )

    return when (val result = apiService.translateSearch(translateRequest)) {
        is TranslateSearchResult.Success -> {
            result.response.data
        }
        is TranslateSearchResult.Error -> {
            "Translation failed: ${result.message}"
        }
        is TranslateSearchResult.Loading -> {
            "Translating..."
        }
    }
}




class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 初始化ApiService的UserManager
        ApiService.getInstance().initUserManager(this)

        setContent {
            MainScreenWithTheme()
        }
    }
}

@Composable
fun MainScreenWithTheme() {
    val context = LocalContext.current
    val settingsManager = remember { SettingsManager(context) }
    var isDarkTheme by remember { mutableStateOf(settingsManager.isDarkTheme) }

    // 监听主题变化
    LaunchedEffect(isDarkTheme) {
        settingsManager.isDarkTheme = isDarkTheme
    }

    TerpMetaAndroidTheme(darkTheme = isDarkTheme) {
        MainScreen(
            isDarkTheme = isDarkTheme,
            onThemeChange = { isDarkTheme = it }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    isDarkTheme: Boolean = false,
    onThemeChange: (Boolean) -> Unit = {}
) {
    val context = LocalContext.current
    val settingsManager = remember { SettingsManager(context) }
    val userManager = remember { com.greenterp.data.UserManager.getInstance(context) }

    var showLogoutDialog by remember { mutableStateOf(false) }
    var showSettingsDialog by remember { mutableStateOf(false) }
    var showVocabularyDialog by remember { mutableStateOf(false) }
    var showReplaceSceneDialog by remember { mutableStateOf(false) }
    var showColorPickerDialog by remember { mutableStateOf(false) }
    var voiceSessions by remember { mutableStateOf(listOf<VoiceSession>()) }
    var drawingSessions by remember { mutableStateOf(listOf<DrawingSession>()) }
    var isRecording by remember { mutableStateOf(false) }

    // 获取当前登录用户信息 - 使用状态来确保能够更新
    var currentUserEmail by remember { mutableStateOf("") }

    // 监听用户登录状态变化，更新用户邮箱显示
    LaunchedEffect(Unit) {
        val userData = userManager.getUserData()
        val email = userData?.emailAddress ?: ""
        currentUserEmail = email
        android.util.Log.d("MainActivity", "初始化获取用户邮箱: $email")
    }

    // 监听应用恢复时的用户数据更新
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = androidx.lifecycle.LifecycleEventObserver { _, event ->
            if (event == androidx.lifecycle.Lifecycle.Event.ON_RESUME) {
                val userData = userManager.getUserData()
                val email = userData?.emailAddress ?: ""
                if (email != currentUserEmail) {
                    currentUserEmail = email
                    android.util.Log.d("MainActivity", "应用恢复时用户邮箱更新: $email")
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    // UI设置 - 从持久化存储中加载
    var fontSize by remember { mutableStateOf(settingsManager.fontSize) }
    // isDarkTheme 现在从参数传入，不再从 settingsManager 读取
    var highlightColor by remember { mutableStateOf(Color(settingsManager.highlightColor)) }
    var searchValue by remember { mutableStateOf("") }

    // 翻译状态管理 - 使用会话概念
    var translationSessions by remember { mutableStateOf<List<TranslationSession>>(emptyList()) }
    var translatedTexts by remember { mutableStateOf<Set<String>>(emptySet()) } // 防重复翻译
    var showScrollButton by remember { mutableStateOf(false) }
    var isDrawingMode by remember { mutableStateOf(false) }
    var canvasDrawingMode by remember { mutableStateOf(true) } // 画布区域的绘制模式状态，默认为绘制状态
    var isLayoutSwapped by remember { mutableStateOf(false) } // 布局交换状态

    // 自动滚动控制状态
    var isAsrAutoScrollEnabled by remember { mutableStateOf(true) } // ASR区域自动滚动开关
    var isTranslationAutoScrollEnabled by remember { mutableStateOf(true) } // 翻译区域自动滚动开关
    var isCanvasAutoScrollEnabled by remember { mutableStateOf(true) } // 画布区域自动滚动开关



    // 画布绘制数据缓存 - sessionId -> 画布数据（包含路径和偏移量）
    var canvasDataCache by remember { mutableStateOf(mutableMapOf<String, com.greenterp.CanvasData>()) }

    // 语言设置 - 从持久化存储中加载
    var primaryLanguage by remember { mutableStateOf(settingsManager.primaryLanguage) }
    var secondaryLanguage by remember { mutableStateOf(settingsManager.secondaryLanguage) }
    var currentLanguageIsPrimary by remember { mutableStateOf(settingsManager.currentLanguageIsPrimary) }

    // 第三语言设置 - 从持久化存储中加载
    var tertiaryLanguage by remember { mutableStateOf(settingsManager.tertiaryLanguage) }
    var tertiaryServiceType by remember { mutableStateOf(settingsManager.tertiaryServiceType) }
    var isAutoLanguageEnabled by remember { mutableStateOf(settingsManager.isAutoLanguageEnabled) }
    // 语音服务设置 - 从持久化存储中加载
    var primaryServiceType by remember { mutableStateOf(settingsManager.primaryVoiceService) }
    var secondaryServiceType by remember { mutableStateOf(settingsManager.secondaryVoiceService) }

    // 三语言配置管理
    var threeLanguageConfig by remember {
        mutableStateOf(
            ThreeLanguageConfig(
                primaryLanguage = primaryLanguage,
                secondaryLanguage = secondaryLanguage,
                tertiaryLanguage = tertiaryLanguage,
                primaryServiceType = primaryServiceType,
                secondaryServiceType = secondaryServiceType,
                tertiaryServiceType = tertiaryServiceType,
                currentLanguageType = if (currentLanguageIsPrimary) LanguageType.PRIMARY else LanguageType.SECONDARY,
                isAutoEnabled = isAutoLanguageEnabled
            )
        )
    }

    // 新的语言管理系统 - 根据Auto开关决定使用二语言还是三语言模式
    var languageList by remember {
        mutableStateOf(
            if (isAutoLanguageEnabled) {
                LanguageConfig.createThreeLanguageList(
                    primaryLanguageShow = primaryLanguage,
                    secondaryLanguageShow = secondaryLanguage,
                    tertiaryLanguageShow = tertiaryLanguage,
                    primaryServiceDisplayName = primaryServiceType,
                    secondaryServiceDisplayName = secondaryServiceType,
                    tertiaryServiceDisplayName = tertiaryServiceType,
                    currentLanguageType = threeLanguageConfig.currentLanguageType.index,
                    isAutoEnabled = isAutoLanguageEnabled
                )
            } else {
                LanguageConfig.createLanguageList(
                    primaryLanguageShow = primaryLanguage,
                    secondaryLanguageShow = secondaryLanguage,
                    serviceDisplayName = primaryServiceType,
                    currentIsPrimary = currentLanguageIsPrimary
                )
            }
        )
    }
    var currentLanguage by remember {
        mutableStateOf(LanguageConfig.getCurrentLanguage(languageList) ?: languageList.first())
    }

    // 翻译相关状态
    var showTranslationPanel by remember { mutableStateOf(false) }
    var translationResult by remember { mutableStateOf<TranslationResult?>(null) }
    var translationModels by remember { mutableStateOf<List<TranslationModel>>(emptyList()) }
    var selectedTranslationModel by remember { mutableStateOf<TranslationModel?>(settingsManager.selectedTranslationModel) }
    var isTranslateActive by remember { mutableStateOf(false) }
    var splitRatio by remember { mutableStateOf(settingsManager.splitRatio) } // 上下分割比例，从设置中加载

    // 翻译逻辑 - 监听语音识别结果变化，使用会话概念
    LaunchedEffect(voiceSessions.hashCode(), selectedTranslationModel, currentLanguageIsPrimary, isTranslateActive) {
        val translationModel = selectedTranslationModel // 创建本地变量避免智能转换问题
        if (translationModel != null && voiceSessions.isNotEmpty() && isTranslateActive) {
            println("翻译逻辑触发 - 会话数量: ${voiceSessions.size}")

            voiceSessions.forEach { voiceSession ->
                // 查找对应的翻译会话
                val existingTranslationSession = translationSessions.find { it.id == voiceSession.id }
                if (existingTranslationSession == null) {
                    println("未找到对应的翻译会话，语音会话ID: ${voiceSession.id}")
                    return@forEach // 跳过这个语音会话
                }
                val currentTranslationSession = existingTranslationSession
                println("找到对应的翻译会话，ID: ${currentTranslationSession.id}")

                // 处理当前语音会话的识别结果
                val updatedTranslationResults = mutableListOf<DataTranslationResult>()
                updatedTranslationResults.addAll(currentTranslationSession.translationResults)

                var hasNewTranslations = false

                // 获取已翻译的最大索引，从下一个索引开始翻译
                val maxTranslatedIndex = currentTranslationSession.translationResults
                    .filter { !it.isIntermediate }
                    .maxOfOrNull { it.index } ?: -1

                println("处理会话: 语音ID=${voiceSession.id}, 翻译ID=${currentTranslationSession.id}")
                println("最大已翻译索引: $maxTranslatedIndex，当前会话ASR结果数量: ${voiceSession.recognitionResults.size}，翻译结果数量: ${currentTranslationSession.translationResults.size}")

                voiceSession.recognitionResults.forEach { result ->
                    if (result.transcript.isNotEmpty()) {
                        // 检查是否需要翻译 - 只翻译新的内容
                        val needsTranslation = if (result.isIntermediate) {
                            // 识别中状态：只翻译当前最新的中间结果
                            result.index >= maxTranslatedIndex && (
                                updatedTranslationResults.find {
                                    it.index == result.index && it.isIntermediate
                                }?.originalText != result.transcript
                            )
                        } else {
                            // 识别完成状态：只翻译索引大于已翻译最大索引的结果
                            result.index > maxTranslatedIndex
                        }

                        if (needsTranslation) {
                            println("开始翻译: ${result.transcript} (isIntermediate: ${result.isIntermediate}, index: ${result.index})")
                            println("ASR结果详情 - source: ${result.source}, languageCode: ${result.languageCode}")
                            try {
                                // 讯飞ASR动态语言检测和翻译方向调整
                                val (fromLang, toLang) = when (result.source) {
                                    "xunfei" -> {
                                        detectLanguageAndGetTranslationPair(result.transcript, languageList)
                                    }
                                    "azure" -> {
                                        // 找到检测到的源语言
                                        val sourceLang = languageList.find { it.code == result.languageCode }
                                            ?: languageList.find { it.code == "zh-CN" } // 默认中文

                                        // 找到目标语言（非源语言且非Auto的第一个语言）
                                        val targetLang = languageList.find { it.code != result.languageCode && it.code != "Auto" }
                                            ?: languageList.find { it.code == "en-US" } // 默认英文
                                        Pair(sourceLang?.code ?: "zh-CN", targetLang?.code ?: "en-US")
                                    }
                                    else -> {
                                        val targetLanguage = LanguageConfig.getTargetLanguage(languageList)
                                        Pair(currentLanguage.code, targetLanguage?.code ?: "en-US")
                                    }
                                }


                                val translatedText = performTranslation(
                                    text = result.transcript,
                                    model = translationModel,
                                    fromLanguage = fromLang,
                                    toLanguage = toLang
                                )

                                println("翻译结果: $translatedText")
                                println("翻译详情 - 从 $fromLang 到 $toLang, 使用模型: ${translationModel.name}")

                                if (!translatedText.startsWith("Translation failed:")) {
                                    val translationResult = DataTranslationResult(
                                        originalText = result.transcript,
                                        translatedText = translatedText,
                                        isIntermediate = result.isIntermediate,
                                        index = result.index
                                    )
                                    println("创建翻译结果 - source: ${result.source}, languageCode: ${result.languageCode}, isIntermediate: ${result.isIntermediate}")

                                    if (result.isIntermediate) {
                                        // 识别中状态 - 更新或添加中间结果
                                        val existingIndex = updatedTranslationResults.indexOfFirst {
                                            it.index == result.index && it.isIntermediate
                                        }
                                        if (existingIndex >= 0) {
                                            updatedTranslationResults[existingIndex] = translationResult
                                        } else {
                                            updatedTranslationResults.add(translationResult)
                                        }
                                    } else {
                                        // 识别完成状态 - 移除对应的中间结果，添加最终结果
                                        updatedTranslationResults.removeAll { it.index == result.index && it.isIntermediate }
                                        updatedTranslationResults.add(translationResult)
                                        // 不再需要维护 translatedTexts，因为我们使用索引来判断
                                    }
                                    hasNewTranslations = true
                                }
                            } catch (e: Exception) {
                                println("Translation failed: ${e.message}")
                            }
                        } else {
                            // 详细的跳过原因分析
                            val skipReason = if (result.isIntermediate) {
                                val indexCondition = result.index >= maxTranslatedIndex
                                val existingTranslation = updatedTranslationResults.find {
                                    it.index == result.index && it.isIntermediate
                                }
                                val contentChanged = existingTranslation?.originalText != result.transcript

                                "中间结果 - 索引条件: $indexCondition (${result.index} >= $maxTranslatedIndex), " +
                                "内容变化: $contentChanged (现有: '${existingTranslation?.originalText}' vs 新: '${result.transcript}')"
                            } else {
                                val indexCondition = result.index > maxTranslatedIndex
                                "最终结果 - 索引条件: $indexCondition (${result.index} > $maxTranslatedIndex)"
                            }

                            println("跳过翻译: ${result.transcript.take(50)}... (isIntermediate: ${result.isIntermediate}, index: ${result.index}) - 原因: $skipReason")
                        }
                    }
                }

                // 只有在有新翻译时才更新会话
                if (hasNewTranslations) {
                    val updatedTranslationSession = currentTranslationSession.copy(
                        translationResults = updatedTranslationResults
                    )

                    // 更新翻译会话列表（只更新现有会话，不创建新会话）
                    translationSessions = translationSessions.map { session ->
                        if (session.id == updatedTranslationSession.id) updatedTranslationSession else session
                    }

                    println("翻译会话已更新 - 翻译结果数量: ${updatedTranslationSession.translationResults.size}")
                    println("当前翻译会话总数: ${translationSessions.size}")
                }
            }
        }
    }

    // 焦点管理
    val focusManager = LocalFocusManager.current
    val searchFocusRequester = remember { FocusRequester() }

    val listState = rememberLazyListState()
    val translationListState = rememberLazyListState()
    val drawingListState = rememberLazyListState() // 画布区域的滚动状态

    // 协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 统一语音识别管理器
    val asrManager = remember { UnifiedAsrManager(context) }

    // 触控笔划词处理函数
    val handleStylusTextSelected = { selectedText: String ->
        // 将选中的文字填入搜索框
        searchValue = selectedText

        // 自动触发查词
        if (selectedText.isNotEmpty()) {
            translationResult = TranslationResult.Loading
            showTranslationPanel = true

            coroutineScope.launch {
                selectedTranslationModel?.let { model ->
                    // 使用新的语言管理系统
                    val targetLanguage = LanguageConfig.getTargetLanguage(languageList)
                    val sourceLangCode = currentLanguage.code
                    val targetLangCode = targetLanguage?.code ?: "en-US"

                    val request = TranslateSearchRequest(
                        text = selectedText,
                        source_lang = sourceLangCode,
                        target_lang = targetLangCode,
                        translateApi = model
                    )

                    val apiService = ApiService.getInstance()
                    when (val result = apiService.translateSearch(request)) {
                        is TranslateSearchResult.Success -> {
                            Log.d("Translation", "划词翻译成功: ${result.response.data}")
                            translationResult = TranslationResult.Success(
                                com.greenterp.network.TranslationResponse(
                                    success = true,
                                    data = com.greenterp.network.TranslationData(
                                        originalText = selectedText,
                                        translatedText = result.response.data,
                                        fromLanguage = sourceLangCode,
                                        toLanguage = targetLangCode
                                    )
                                )
                            )
                        }
                        is TranslateSearchResult.Error -> {
                            Log.e("Translation", "划词翻译失败: ${result.message}")
                            translationResult = TranslationResult.Error(result.message)
                        }
                        is TranslateSearchResult.Loading -> {
                            Log.d("Translation", "划词翻译中...")
                            // Loading状态已经在上面设置了
                        }
                    }
                }
            }
        }
    }

    // 词汇表管理器
    val glossaryManager = remember { GlossaryManager(context) }

    // 替换表管理器
    val replaceManager: ReplaceManager = remember { ReplaceManager(context) }

    // 监听识别状态
    val isAsrRecording by asrManager.isRecording.collectAsState()
    val recognitionResults by asrManager.recognitionResults.collectAsState()
    val asrError by asrManager.errorMessage.collectAsState()
    val connectionState by asrManager.connectionState.collectAsState()

    // 监听词汇表状态
    val glossaryItems by glossaryManager.glossaryItems.collectAsState()
    val isGlossaryLoading by glossaryManager.isLoading.collectAsState()
    val glossaryError by glossaryManager.errorMessage.collectAsState()

    // 监听语言设置变化，更新三语言配置和语言列表
    LaunchedEffect(
        primaryLanguage,
        secondaryLanguage,
        tertiaryLanguage,
        primaryServiceType,
        secondaryServiceType,
        tertiaryServiceType,
        currentLanguageIsPrimary,
        isAutoLanguageEnabled
    ) {
        // 更新三语言配置
        threeLanguageConfig = ThreeLanguageConfig(
            primaryLanguage = primaryLanguage,
            secondaryLanguage = secondaryLanguage,
            tertiaryLanguage = tertiaryLanguage,
            primaryServiceType = primaryServiceType,
            secondaryServiceType = secondaryServiceType,
            tertiaryServiceType = tertiaryServiceType,
            currentLanguageType = if (currentLanguageIsPrimary) LanguageType.PRIMARY else LanguageType.SECONDARY,
            isAutoEnabled = isAutoLanguageEnabled
        )

        // 根据Auto开关决定使用二语言还是三语言模式
        languageList = if (isAutoLanguageEnabled) {
            LanguageConfig.createThreeLanguageList(
                primaryLanguageShow = primaryLanguage,
                secondaryLanguageShow = secondaryLanguage,
                tertiaryLanguageShow = tertiaryLanguage,
                primaryServiceDisplayName = primaryServiceType,
                secondaryServiceDisplayName = secondaryServiceType,
                tertiaryServiceDisplayName = tertiaryServiceType,
                currentLanguageType = threeLanguageConfig.currentLanguageType.index,
                isAutoEnabled = isAutoLanguageEnabled
            )
        } else {
            LanguageConfig.createLanguageList(
                primaryLanguageShow = primaryLanguage,
                secondaryLanguageShow = secondaryLanguage,
                serviceDisplayName = primaryServiceType,
                currentIsPrimary = currentLanguageIsPrimary
            )
        }
        currentLanguage = LanguageConfig.getCurrentLanguage(languageList) ?: languageList.first()
    }

    // 更新ASR管理器配置 - 支持三语言模式
    LaunchedEffect(
        primaryLanguage,
        secondaryLanguage,
        tertiaryLanguage,
        primaryServiceType,
        secondaryServiceType,
        tertiaryServiceType,
        threeLanguageConfig.currentLanguageType,
        isAutoLanguageEnabled
    ) {
        asrManager.setThreeLanguageConfiguration(
            primaryLang = primaryLanguage,
            secondaryLang = secondaryLanguage,
            tertiaryLang = tertiaryLanguage,
            primaryService = primaryServiceType,
            secondaryService = secondaryServiceType,
            tertiaryService = tertiaryServiceType,
            currentLanguageType = threeLanguageConfig.currentLanguageType,
            isAutoEnabled = isAutoLanguageEnabled
        )
    }

    // 开始新的语音识别会话的函数
    fun startNewVoiceSession(
        asrManager: UnifiedAsrManager,
        currentSessions: List<VoiceSession>,
        onSessionsUpdate: (List<VoiceSession>) -> Unit,
        onCreateTranslationSession: (String, String) -> Unit = { _, _ -> }  // 新增创建翻译会话的回调
    ) {
        // 创建新的语音识别会话
        val newSession = VoiceSession(
            timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        )

        // 更新会话列表
        val updatedSessions = listOf(newSession) + currentSessions
        onSessionsUpdate(updatedSessions)

        // 清空之前的识别结果
        asrManager.clearResults()

        // 创建对应的翻译会话
        onCreateTranslationSession(newSession.id, newSession.timestamp)

        // 开始语音识别
        asrManager.startRecognition()

        println("Start按钮: 开始新的语音识别会话")
    }

    // 创建新的画布会话的函数
    fun createNewDrawingSession(
        currentSessions: List<DrawingSession>,
        onSessionsUpdate: (List<DrawingSession>) -> Unit
    ) {
        // 创建新的画布会话
        val newSession = DrawingSession(
            timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date()),
            isDrawingMode = true
        )

        // 为新画布初始化空的画布数据
        canvasDataCache = canvasDataCache.toMutableMap().apply {
            put(newSession.id, com.greenterp.CanvasData())
        }

        // 更新会话列表
        val updatedSessions = listOf(newSession) + currentSessions
        onSessionsUpdate(updatedSessions)
    }

    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // 权限获取成功，开始语音识别
            startNewVoiceSession(
                asrManager = asrManager,
                currentSessions = voiceSessions,
                onSessionsUpdate = { newSessions ->
                    voiceSessions = newSessions
                },
                onCreateTranslationSession = { voiceSessionId, timestamp ->
                    // 创建新的空翻译会话，使用与语音会话相同的ID
                    val newTranslationSession = TranslationSession(
                        id = voiceSessionId,
                        timestamp = timestamp
                    )
                    translationSessions = listOf(newTranslationSession) + translationSessions
                    translatedTexts = emptySet()
                }
            )
        } else {
            Toast.makeText(context, "Recording permission is required for voice recognition", Toast.LENGTH_SHORT).show()
        }
    }

    // 自动滚动到底部的函数
    val scrollToBottom = {
        coroutineScope.launch {
            if (voiceSessions.isNotEmpty() && isAsrAutoScrollEnabled) {
                listState.animateScrollToItem(0) // 滚动到最新的会话（索引0）
                // 重置滚动按钮状态
                showScrollButton = false
            }
        }
    }

    // 翻译面板自动滚动到底部的函数
    val scrollTranslationToBottom = {
        coroutineScope.launch {
            if (translationSessions.isNotEmpty() && isTranslationAutoScrollEnabled) {
                translationListState.animateScrollToItem(0) // 滚动到最新的翻译会话（索引0）
            }
        }
    }

    // 画布区域自动滚动到底部的函数
    val scrollCanvasToBottom = {
        coroutineScope.launch {
            if (drawingSessions.isNotEmpty() && isCanvasAutoScrollEnabled) {
                drawingListState.animateScrollToItem(0) // 滚动到最新的画布会话（索引0）
            }
        }
    }

    // 所有区域同时自动滚动到底部的函数
    val scrollAllToBottom = {
        coroutineScope.launch {
            val asrScrollJob = async {
                if (voiceSessions.isNotEmpty() && isAsrAutoScrollEnabled) {
                    listState.animateScrollToItem(0)
                    showScrollButton = false
                }
            }

            val translationScrollJob = async {
                if (translationSessions.isNotEmpty() && isTranslationAutoScrollEnabled) {
                    translationListState.animateScrollToItem(0)
                }
            }

            val canvasScrollJob = async {
                if (drawingSessions.isNotEmpty() && isCanvasAutoScrollEnabled) {
                    drawingListState.animateScrollToItem(0)
                }
            }

            // 等待所有滚动操作完成
            asrScrollJob.await()
            translationScrollJob.await()
            canvasScrollJob.await()

            println("所有区域同时滚动到最新位置")
        }
    }

    // 滚动到特定session的函数（支持内容顶部对齐）
    val scrollToSpecificSession = { sessionIndex: Int ->
        coroutineScope.launch {
            // 启动三个并发的滚动操作，但不等待完成
            launch {
                if (voiceSessions.isNotEmpty() && sessionIndex < voiceSessions.size) {
                    listState.animateScrollToItem(sessionIndex)
                    println("ASR区域滚动到session index: $sessionIndex")
                }
            }

            launch {
                if (translationSessions.isNotEmpty() && sessionIndex < translationSessions.size) {
                    translationListState.animateScrollToItem(sessionIndex)
                    println("翻译区域滚动到session index: $sessionIndex")
                }
            }

            launch {
                if (drawingSessions.isNotEmpty() && sessionIndex < drawingSessions.size) {
                    // 恢复动画滚动，但在并发环境中执行
                    drawingListState.animateScrollToItem(sessionIndex)
                    println("画布区域滚动到session index: $sessionIndex")
                }
            }

            println("启动所有区域滚动到session index: $sessionIndex")
        }
    }

    // 监听滚动状态
    LaunchedEffect(listState.isScrollInProgress) {
        if (!listState.isScrollInProgress) {
            // 检查是否在顶部（最新内容）
            val firstVisibleIndex = listState.firstVisibleItemIndex
            val firstVisibleOffset = listState.firstVisibleItemScrollOffset
            showScrollButton = firstVisibleIndex > 0 || firstVisibleOffset > 100
        }
    }

    // 处理识别结果更新
    LaunchedEffect(recognitionResults) {
        if (recognitionResults.isNotEmpty() && voiceSessions.isNotEmpty()) {
            // 打印所有识别结果的详细信息
            recognitionResults.forEach { result ->
                println("识别结果更新 - text: ${result.transcript}, source: ${result.source}, languageCode: ${result.languageCode}, isIntermediate: ${result.isIntermediate}, index: ${result.index}")
            }

            val currentSession = voiceSessions[0]
            val updatedSession =
                currentSession.copy(recognitionResults = recognitionResults.toMutableList())
            val updatedSessions = voiceSessions.toMutableList()
            updatedSessions[0] = updatedSession
            voiceSessions = updatedSessions.toList()

            // 自动滚动到底部（包括中间结果）- 只有在自动滚动开启时才执行
            if (isAsrAutoScrollEnabled) {
                scrollToBottom()
            }
        }
    }

    // 监听语音会话中识别结果的变化，确保所有变化都触发滚动
    LaunchedEffect(voiceSessions.firstOrNull()?.recognitionResults?.hashCode(), isAsrAutoScrollEnabled) {
        if (voiceSessions.isNotEmpty() && voiceSessions[0].recognitionResults.isNotEmpty() && isAsrAutoScrollEnabled) {
            scrollToBottom()
        }
    }

    // 处理错误信息
    LaunchedEffect(asrError) {
        asrError?.let { error ->
            Toast.makeText(context, "Voice recognition error: $error", Toast.LENGTH_SHORT).show()
            asrManager.clearError()
        }
    }

    // 同步录音状态
    LaunchedEffect(isAsrRecording) {
        isRecording = isAsrRecording
    }

    // 监听翻译会话变化，自动滚动到底部
    LaunchedEffect(translationSessions.hashCode(), isTranslationAutoScrollEnabled) {
        if (translationSessions.isNotEmpty() && isTranslationAutoScrollEnabled) {
            // 当自动滚动开启时，总是滚动到最新会话
            scrollTranslationToBottom()
        }
    }

    // 监听翻译结果变化，确保自动滚动（监听内容变化，包括中间结果）
    LaunchedEffect(translationSessions.firstOrNull()?.translationResults?.hashCode(), isTranslationAutoScrollEnabled) {
        if (translationSessions.isNotEmpty() && isTranslationAutoScrollEnabled) {
            val latestSession = translationSessions.first()
            if (latestSession.translationResults.isNotEmpty()) {
                scrollTranslationToBottom()
            }
        }
    }

    // 监听画布会话变化，自动滚动到底部
    LaunchedEffect(drawingSessions.size, drawingSessions.firstOrNull()?.id, isCanvasAutoScrollEnabled) {
        if (drawingSessions.isNotEmpty() && isCanvasAutoScrollEnabled) {
            scrollCanvasToBottom()
        }
    }

    // 监听自动滚动状态变化，当开启时立即滚动到最新位置
    LaunchedEffect(isTranslationAutoScrollEnabled) {
        if (isTranslationAutoScrollEnabled && translationSessions.isNotEmpty()) {
            scrollTranslationToBottom()
        }
    }

    // 监听语音识别状态变化，当开始录音时滚动到最新位置
    LaunchedEffect(isRecording) {
        if (isRecording) {
            // 开启自动滚动
            isAsrAutoScrollEnabled = true
            isTranslationAutoScrollEnabled = true
            isCanvasAutoScrollEnabled = true

            // 滚动到最新位置
            scrollToSpecificSession(0)
            println("Start按钮: 检测到开始录音，滚动到最新session")
        }
    }

    // 应用启动时自动加载保存的词汇表
    LaunchedEffect(Unit) {
        val settingsManager = SettingsManager(context)
        if (settingsManager.selectedVocabularyId != -1) {
            val success = glossaryManager.loadGlossaryBySceneIds(listOf(settingsManager.selectedVocabularyId))
            if (success) {
                println("应用启动: 自动加载保存的词汇表 ${settingsManager.selectedVocabularyName}, 共 ${glossaryManager.getGlossaryCount()} 条记录")
            } else {
                println("应用启动: 加载保存的词汇表失败")
            }
        }
    }

    // 应用启动时自动加载保存的替换表
    LaunchedEffect(Unit) {
        val settingsManager = SettingsManager(context)
        if (settingsManager.selectedReplaceId != -1) {
            val success = replaceManager.loadReplacesBySceneId(settingsManager.selectedReplaceId)
            if (success) {
                Log.d("MainActivity", "应用启动: 自动加载保存的替换表 ${settingsManager.selectedReplaceName}, 共 ${replaceManager.getReplaceCount()} 条记录")
            } else {
                Log.e("MainActivity", "应用启动: 加载保存的替换表失败: ${replaceManager.errorMessage.value}")
            }
        }
    }

    // 获取当前显示的session索引
    val getCurrentSessionIndex = {
        listState.firstVisibleItemIndex
    }



    // Back按钮功能：根据当前位置和语音识别状态决定行为
    val handleBackClick = {
        // 检查当前是否在最新session（索引0）
        val currentSessionIndex = getCurrentSessionIndex()

        if (currentSessionIndex == 0) {
            // 在最新session：检查是否正在语音识别
            if (isRecording) {
                // 正在语音识别：新增session并切换到倒数第二个
                println("Back按钮: 正在语音识别，新增session")

                // 先关闭自动滚动，防止新增会话时自动滚动干扰
                isAsrAutoScrollEnabled = false
                isTranslationAutoScrollEnabled = false
                isCanvasAutoScrollEnabled = false
                println("Back按钮: 先关闭自动滚动")

                val currentTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

                // 新增语音会话
                val newVoiceSession = VoiceSession(timestamp = currentTime)
                voiceSessions = listOf(newVoiceSession) + voiceSessions

                // 新增翻译会话（使用相同的ID，清空翻译结果，从头开始）
                val newTranslationSession = TranslationSession(
                    id = newVoiceSession.id, // 使用相同的ID确保关联
                    timestamp = currentTime
                )
                translationSessions = listOf(newTranslationSession) + translationSessions

                // 新增画布会话
                val newDrawingSession = DrawingSession(timestamp = currentTime, isDrawingMode = true)
                canvasDataCache = canvasDataCache.toMutableMap().apply {
                    put(newDrawingSession.id, com.greenterp.CanvasData())
                }
                drawingSessions = listOf(newDrawingSession) + drawingSessions

                // 清空已翻译文本集合，让新session可以重新翻译
                translatedTexts = emptySet()

                // 清空ASR识别结果，确保新session从干净状态开始
                asrManager.clearResults()

                println("Back按钮: 创建新会话 - 语音ID: ${newVoiceSession.id}, 翻译ID: ${newTranslationSession.id}")

                // 滚动到倒数第二个session（新创建后的索引1）
                coroutineScope.launch {
                    kotlinx.coroutines.delay(150) // 增加延迟确保UI更新完成
                    scrollToSpecificSession(1)
                    println("Back按钮: 滚动到倒数第二个session (index 1)")
                }
            } else {
                // 没有在语音识别：只切换到上一个session（如果存在）
                println("Back按钮: 没有在语音识别，切换到上一个session")

                if (voiceSessions.size > 1) {
                    // 关闭自动滚动
                    isAsrAutoScrollEnabled = false
                    isTranslationAutoScrollEnabled = false
                    isCanvasAutoScrollEnabled = false

                    // 滚动到下一个session（索引1）
                    coroutineScope.launch {
                        scrollToSpecificSession(1)
                        println("Back按钮: 滚动到第2个session (index 1)")
                    }
                } else {
                    println("Back按钮: 只有一个session，无法向上滚动")
                }
            }
        } else {
            // 在其他session：只切换到上一个session
            println("Back按钮: 当前在第${currentSessionIndex + 1}个session，切换到上一个session")

            val targetIndex = currentSessionIndex + 1
            if (targetIndex < voiceSessions.size) {
                // 关闭自动滚动（如果还没关闭）
                isAsrAutoScrollEnabled = false
                isTranslationAutoScrollEnabled = false
                isCanvasAutoScrollEnabled = false

                // 滚动到目标session
                coroutineScope.launch {
                    scrollToSpecificSession(targetIndex)
                    println("Back按钮: 滚动到第${targetIndex + 1}个session (index $targetIndex)")
                }
            } else {
                println("Back按钮: 已经是最后一个session，无法继续向后")
            }
        }
        Unit // 明确返回Unit
    }

    // Next按钮功能：创建新的会话（ASR + 翻译 + 画布同步新增）
    val handleNextClick = {
        println("Next按钮: 创建新的会话")

        // 先关闭自动滚动，防止创建会话时自动滚动干扰
        isAsrAutoScrollEnabled = false
        isTranslationAutoScrollEnabled = false
        isCanvasAutoScrollEnabled = false

        val currentTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

        // 1. 新增语音会话
        val newVoiceSession = VoiceSession(timestamp = currentTime)
        voiceSessions = listOf(newVoiceSession) + voiceSessions

        // 2. 新增翻译会话（使用相同的ID，确保关联）
        val newTranslationSession = TranslationSession(
            id = newVoiceSession.id, // 使用相同的ID确保关联
            timestamp = currentTime
        )
        translationSessions = listOf(newTranslationSession) + translationSessions

        // 3. 新增画布会话
        val newDrawingSession = DrawingSession(timestamp = currentTime, isDrawingMode = true)
        canvasDataCache = canvasDataCache.toMutableMap().apply {
            put(newDrawingSession.id, com.greenterp.CanvasData())
        }
        drawingSessions = listOf(newDrawingSession) + drawingSessions

        // 4. 清空已翻译文本集合，让新session可以重新翻译
        translatedTexts = emptySet()

        // 5. 如果正在进行ASR，清空ASR识别结果和当前会话内容，让ASR内容输出到新创建的面板
        if (isRecording) {
            // 清空ASR管理器的识别结果
            asrManager.clearResults()

            // 清空当前会话（旧的第一个会话）的识别结果，避免显示之前的内容
            if (voiceSessions.isNotEmpty()) {
                val currentSession = voiceSessions[0]
                currentSession.recognitionResults.clear()
                println("Next按钮: 清空当前会话的识别结果，会话ID: ${currentSession.id}")
            }

            println("Next按钮: 正在进行ASR，清空识别结果，ASR内容将输出到新面板")
        }

        println("Next按钮: 创建新会话 - 语音ID: ${newVoiceSession.id}, 翻译ID: ${newTranslationSession.id}, 画布ID: ${newDrawingSession.id}")

        // 6. 滚动到最新session（新创建的session，索引0）并开启自动滚动
        coroutineScope.launch {
            kotlinx.coroutines.delay(150) // 等待UI更新完成
            scrollToSpecificSession(0)

            // 开启自动滚动，确保新内容能自动滚动到底部
            isAsrAutoScrollEnabled = true
            isTranslationAutoScrollEnabled = true
            isCanvasAutoScrollEnabled = true

            println("Next按钮: 滚动到新创建的session (index 0) 并开启自动滚动")
        }

        Unit // 明确返回Unit
    }

    // 加载翻译模型列表
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            val result = ApiService.getInstance().getTranslationModelList()
            when (result) {
                is TranslationModelListResult.Loading -> {
                    // Do nothing
                }

                is TranslationModelListResult.Success -> {
                    if (result.response.success) {
                        translationModels = result.response.data

                        // 验证当前选中的翻译模型是否仍然有效
                        val currentModel = selectedTranslationModel
                        val isCurrentModelValid = currentModel != null &&
                            translationModels.any { it.id == currentModel.id }

                        if (!isCurrentModelValid) {
                            // 如果当前模型无效或不存在，重新选择模型
                            selectedTranslationModel = translationModels.firstOrNull { it.enable == 1 }
                                ?: translationModels.firstOrNull()
                            selectedTranslationModel?.let {
                                settingsManager.selectedTranslationModel = it
                                Log.d("MainActivity", "翻译模型已更新: ${it.name} (ID: ${it.id})")
                            }
                        } else {
                            // 如果当前模型仍然有效，但需要更新模型的详细信息（如key等）
                            val updatedModel = translationModels.find { it.id == currentModel.id }
                            if (updatedModel != null && updatedModel != currentModel) {
                                selectedTranslationModel = updatedModel
                                settingsManager.selectedTranslationModel = updatedModel
                                Log.d("MainActivity", "翻译模型信息已更新: ${updatedModel.name} (ID: ${updatedModel.id})")
                            }
                        }
                    } else {
                        Toast.makeText(
                            context,
                            "Failed to get translation models: ${result.response.msg}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }

                is TranslationModelListResult.Error -> {
                    Toast.makeText(
                        context,
                        "Failed to get translation models: ${result.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            TopBar(
                searchValue = searchValue,
                onSearchValueChange = { searchValue = it },
                translationModels = translationModels,
                selectedTranslationModel = selectedTranslationModel,
                onTranslationModelSelected = {
                    selectedTranslationModel = it
                    settingsManager.selectedTranslationModel = it
                },
                onSearchSubmit = { query ->
                    if (query.isNotEmpty()) {
                        translationResult = TranslationResult.Loading
                        showTranslationPanel = true
                        Log.d(
                            "ApiService",
                            "selectedTranslationModel model list response: ${selectedTranslationModel}"
                        )
                        coroutineScope.launch {
                            selectedTranslationModel?.let { model ->
                                // 使用新的语言管理系统
                                val targetLanguage = LanguageConfig.getTargetLanguage(languageList)
                                val sourceLangCode = currentLanguage.code
                                val targetLangCode = targetLanguage?.code ?: "en-US"

                                val request = TranslateSearchRequest(
                                    text = query,
                                    source_lang = sourceLangCode,
                                    target_lang = targetLangCode,
                                    translateApi = model
                                )

                                when (val result =
                                    ApiService.getInstance().translateSearch(request)) {
                                    is TranslateSearchResult.Success -> {
                                        Log.d("Translation", "翻译成功: ${result.response.data}")
                                        translationResult = TranslationResult.Success(
                                            com.greenterp.network.TranslationResponse(
                                                success = true,
                                                data = com.greenterp.network.TranslationData(
                                                    originalText = query,
                                                    translatedText = result.response.data,
                                                    fromLanguage = sourceLangCode,
                                                    toLanguage = targetLangCode
                                                )
                                            )
                                        )
                                    }

                                    is TranslateSearchResult.Error -> {
                                        Log.e("Translation", "翻译失败: ${result.message}")
                                        translationResult = TranslationResult.Error(result.message)
                                    }

                                    is TranslateSearchResult.Loading -> {
                                        Log.d("Translation", "翻译中...")
                                    }
                                }
                            } ?: run {
                                Log.e("Translation", "No translation model selected")
                                translationResult = TranslationResult.Error("Please select a translation model first")
                            }
                        }
                    }
                },
                onDrawingClick = {
                    if (!isDrawingMode) {
                        // 如果当前不是绘制模式，切换到绘制模式并创建新的画布会话
                        isDrawingMode = true
                        createNewDrawingSession(drawingSessions) { newSessions ->
                            drawingSessions = newSessions
                        }
                    } else {
                        // 如果已经是绘制模式，隐藏画布
                        isDrawingMode = false
                    }
                },
                onLogoutClick = { showLogoutDialog = true },
                showTranslationPanel = showTranslationPanel,
                translationResult = translationResult,
                primaryLanguage = primaryLanguage,
                secondaryLanguage = secondaryLanguage,
                currentLanguageIsPrimary = currentLanguageIsPrimary,
                onCloseTranslationPanel = {
                    showTranslationPanel = false
                    searchValue = "" // 关闭翻译面板时清空搜索框
                },
                onClearFocus = {
                    focusManager.clearFocus()
                },
                userEmail = currentUserEmail,
                isDarkTheme = isDarkTheme
            )
        },
        bottomBar = {
            BottomActionBar(
                isRecording = isRecording,
                connectionState = connectionState,
                primaryLanguage = primaryLanguage,
                secondaryLanguage = secondaryLanguage,
                currentLanguageIsPrimary = currentLanguageIsPrimary,
                isTranslateActive = isTranslateActive,
                // 传递当前选中的语言显示名称
                currentLanguageDisplay = currentLanguage.show,
                onSettingsClick = { showSettingsDialog = true },
                onDictionaryClick = {
                    showVocabularyDialog = true
                },
                onReplaceClick = {
                    showReplaceSceneDialog = true
                },
                onTranslateClick = {
                    // 切换翻译按钮状态
                    isTranslateActive = !isTranslateActive
                    // TODO: 实现文本翻译功能
                    // 可以打开一个翻译对话框或者跳转到翻译页面
                },
                onStartStopClick = {
                    if (isRecording || connectionState == "connecting") {
                        // 停止语音识别
                        asrManager.stopRecognition()
                    } else {
                        // 检查权限并开始语音识别
                        if (asrManager.hasRecordPermission()) {
                            startNewVoiceSession(
                                asrManager = asrManager,
                                currentSessions = voiceSessions,
                                onSessionsUpdate = { newSessions ->
                                    voiceSessions = newSessions
                                },
                                onCreateTranslationSession = { voiceSessionId, timestamp ->
                                    // 创建新的空翻译会话，使用与语音会话相同的ID
                                    val newTranslationSession = TranslationSession(
                                        id = voiceSessionId,
                                        timestamp = timestamp
                                    )
                                    translationSessions = listOf(newTranslationSession) + translationSessions
                                    translatedTexts = emptySet()
                                }
                            )
                            // 如果处于绘制模式，同时创建新的画布会话
                            if (isDrawingMode) {
                                createNewDrawingSession(drawingSessions) { newSessions ->
                                    drawingSessions = newSessions
                                }
                                // 确保新画布是绘制模式
                                canvasDrawingMode = true
                            }
                        } else {
                            // 请求录音权限
                            permissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
                        }
                    }
                },
                onLanguageToggle = {
                    // 如果正在录音或连接中，先停止
                    if (isRecording || connectionState == "connecting") {
                        asrManager.stopRecognition()
                    }

                    if (isAutoLanguageEnabled) {
                        // 三语言模式：使用ThreeLanguageConfig进行切换
                        threeLanguageConfig = threeLanguageConfig.switchToNext()

                        // 重新创建语言列表
                        languageList = LanguageConfig.createThreeLanguageList(
                            primaryLanguageShow = primaryLanguage,
                            secondaryLanguageShow = secondaryLanguage,
                            tertiaryLanguageShow = tertiaryLanguage,
                            primaryServiceDisplayName = primaryServiceType,
                            secondaryServiceDisplayName = secondaryServiceType,
                            tertiaryServiceDisplayName = tertiaryServiceType,
                            currentLanguageType = threeLanguageConfig.currentLanguageType.index,
                            isAutoEnabled = isAutoLanguageEnabled
                        )

                        // 更新当前语言
                        currentLanguage = LanguageConfig.getCurrentLanguage(languageList) ?: languageList.first()

                        // 更新旧的状态变量以保持兼容性
                        when (threeLanguageConfig.currentLanguageType) {
                            LanguageType.PRIMARY -> {
                                currentLanguageIsPrimary = true
                                settingsManager.currentLanguageIsPrimary = true
                            }
                            LanguageType.SECONDARY -> {
                                currentLanguageIsPrimary = false
                                settingsManager.currentLanguageIsPrimary = false
                            }
                            LanguageType.TERTIARY -> {
                                // Auto语言时，保持当前的主次语言状态不变
                                // 或者可以设置一个特殊的状态标识
                            }
                        }
                    } else {
                        // 二语言模式：使用原有的切换逻辑
                        val (updatedList, newCurrentLanguage) = LanguageConfig.switchToNextLanguage(languageList)
                        languageList = updatedList
                        currentLanguage = newCurrentLanguage

                        // 更新旧的状态变量以保持兼容性
                        currentLanguageIsPrimary = newCurrentLanguage.main
                        settingsManager.currentLanguageIsPrimary = currentLanguageIsPrimary
                    }
                },
                isDarkTheme = isDarkTheme
            )
        },
        containerColor = if (isDarkTheme) Color(0xFF1E1E1E) else Color.White
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(showTranslationPanel) {
                    detectTapGestures(
                        onTap = {
                            // 点击主内容区域时关闭翻译面板和键盘焦点
                            if (showTranslationPanel) {
                                showTranslationPanel = false
                                searchValue = "" // 关闭翻译面板时清空搜索框
                            }
                            focusManager.clearFocus()
                        }
                    )
                }
        ) {
            if (isDrawingMode) {
                // 分屏模式：根据isLayoutSwapped状态决定组件顺序
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                ) {
                    if (!isLayoutSwapped) {
                        // 默认布局：左边语音识别，右边画布
                        if (isTranslateActive) {
                            // 翻译模式：上下分割布局
                            SplitVoiceAndTranslateDisplay(
                                sessions = voiceSessions,
                                fontSize = fontSize,
                                isDarkTheme = isDarkTheme,
                                searchValue = searchValue,
                                splitRatio = splitRatio,
                                onSplitRatioChange = {
                                    splitRatio = it
                                    settingsManager.splitRatio = it
                                },
                                listState = listState,
                                translationSessions = translationSessions,
                                translationListState = translationListState,
                                glossaryManager = glossaryManager, // 传递词汇表管理器
                                replaceManager = replaceManager, // 传递替换表管理器
                                onStylusTextSelected = null, // 暂时禁用触控笔划词功能 - handleStylusTextSelected
                                highlightColor = highlightColor, // 传递高亮颜色
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxHeight()
                            )
                        } else {
                            // 普通模式：只显示语音识别
                            VoiceRecognitionDisplay(
                                sessions = voiceSessions,
                                fontSize = fontSize,
                                isDarkTheme = isDarkTheme,
                                searchValue = searchValue,
                                glossaryManager = glossaryManager, // 传递词汇表管理器
                                replaceManager = replaceManager, // 传递替换表管理器
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxHeight(),
                                listState = listState
                            )
                        }

                        // 分隔线
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(1.dp)
                                .background(Color(0xFFE0E0E0))
                        )

                        DrawingCanvasDisplay(
                            sessions = drawingSessions,
                            canvasDataCache = canvasDataCache,
                            onCanvasDataChanged = { sessionId, canvasData ->
                                println("MainActivity onCanvasDataChanged: sessionId=$sessionId, pathsCount=${canvasData.paths.size}")
                                canvasDataCache = canvasDataCache.toMutableMap().apply {
                                    put(sessionId, canvasData)
                                }
                            },
                            coroutineScope = coroutineScope,
                            globalDrawingMode = canvasDrawingMode,
                            onGlobalDrawingModeChanged = { newMode ->
                                canvasDrawingMode = newMode
                            },
                            onLayoutSwapRequested = {
                                isLayoutSwapped = !isLayoutSwapped
                            },
                            onBackClick = handleBackClick,
                            onNextClick = handleNextClick,
                            isAutoScrollEnabled = isCanvasAutoScrollEnabled,
                            isDarkTheme = isDarkTheme, // 传递主题参数
                            listState = drawingListState,
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight()
                        )
                    } else {
                        // 交换布局：左边画布，右边语音识别
                        DrawingCanvasDisplay(
                            sessions = drawingSessions,
                            canvasDataCache = canvasDataCache,
                            onCanvasDataChanged = { sessionId, canvasData ->
                                println("MainActivity onCanvasDataChanged: sessionId=$sessionId, pathsCount=${canvasData.paths.size}")
                                canvasDataCache = canvasDataCache.toMutableMap().apply {
                                    put(sessionId, canvasData)
                                }
                            },
                            coroutineScope = coroutineScope,
                            globalDrawingMode = canvasDrawingMode,
                            onGlobalDrawingModeChanged = { newMode ->
                                canvasDrawingMode = newMode
                            },
                            onLayoutSwapRequested = {
                                isLayoutSwapped = !isLayoutSwapped
                            },
                            onBackClick = handleBackClick,
                            onNextClick = handleNextClick,
                            isAutoScrollEnabled = isCanvasAutoScrollEnabled,
                            isDarkTheme = isDarkTheme, // 传递主题参数
                            listState = drawingListState,
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight()
                        )

                        // 分隔线
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(1.dp)
                                .background(Color(0xFFE0E0E0))
                        )

                        if (isTranslateActive) {
                            // 翻译模式：上下分割布局
                            SplitVoiceAndTranslateDisplay(
                                sessions = voiceSessions,
                                fontSize = fontSize,
                                isDarkTheme = isDarkTheme,
                                searchValue = searchValue,
                                splitRatio = splitRatio,
                                onSplitRatioChange = {
                                    splitRatio = it
                                    settingsManager.splitRatio = it
                                },
                                listState = listState,
                                translationSessions = translationSessions,
                                translationListState = translationListState,
                                glossaryManager = glossaryManager, // 传递词汇表管理器
                                replaceManager = replaceManager, // 传递替换表管理器
                                onStylusTextSelected = null, // 暂时禁用触控笔划词功能 - handleStylusTextSelected
                                highlightColor = highlightColor, // 传递高亮颜色
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxHeight()
                            )
                        } else {
                            // 普通模式：只显示语音识别
                            VoiceRecognitionDisplay(
                                sessions = voiceSessions,
                                fontSize = fontSize,
                                isDarkTheme = isDarkTheme,
                                searchValue = searchValue,
                                glossaryManager = glossaryManager, // 传递词汇表管理器
                                replaceManager = replaceManager, // 传递替换表管理器
                                highlightColor = highlightColor, // 传递高亮颜色
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxHeight(),
                                listState = listState
                            )
                        }
                    }
                }
            } else {
                // 全屏语音识别模式
                if (isTranslateActive) {
                    // 翻译模式：上下分割布局
                    SplitVoiceAndTranslateDisplay(
                        sessions = voiceSessions,
                        fontSize = fontSize,
                        isDarkTheme = isDarkTheme,
                        searchValue = searchValue,
                        splitRatio = splitRatio,
                        onSplitRatioChange = {
                            splitRatio = it
                            settingsManager.splitRatio = it
                        },
                        listState = listState,
                        translationSessions = translationSessions,
                        translationListState = translationListState,
                        glossaryManager = glossaryManager, // 传递词汇表管理器
                        replaceManager = replaceManager, // 传递替换表管理器
                        onStylusTextSelected = null, // 暂时禁用触控笔划词功能 - handleStylusTextSelected
                        highlightColor = highlightColor, // 传递高亮颜色
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(paddingValues)
                    )
                } else {
                    // 普通模式：只显示语音识别
                    VoiceRecognitionDisplay(
                        sessions = voiceSessions,
                        fontSize = fontSize,
                        isDarkTheme = isDarkTheme,
                        searchValue = searchValue,
                        glossaryManager = glossaryManager, // 传递词汇表管理器
                        replaceManager = replaceManager, // 传递替换表管理器
                        onStylusTextSelected = null, // 暂时禁用触控笔划词功能 - handleStylusTextSelected
                        highlightColor = highlightColor, // 传递高亮颜色
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(paddingValues),
                        listState = listState
                    )
                }
            }

            // 滚动到底部的浮动按钮
            if (showScrollButton && false) {
                FloatingActionButton(
                    onClick = {
                        coroutineScope.launch {
                            listState.animateScrollToItem(0)
                            showScrollButton = false
                        }
                    },
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(end = 40.dp, bottom = 200.dp)
                        .size(40.dp)
                        .zIndex(99f),
                    containerColor = Color(0xFF07c160),
                    contentColor = Color.White,
                    shape = CircleShape
                ) {
                    Icon(
                        Icons.Default.KeyboardArrowDown,
                        contentDescription = "滚动到底部",
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }

    // 登出确认对话框
    if (showLogoutDialog) {
        AlertDialog(
            onDismissRequest = { showLogoutDialog = false },
            title = {
                Text(
                    text = "Confirm Logout",
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text("Are you sure you want to logout?")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showLogoutDialog = false

                        // 清除用户数据
                        userManager.clearUserData()

                        // 清除词汇表和替换表缓存
                        glossaryManager.clearGlossary()
                        replaceManager.clearReplace()

                        // 返回到登录页面
                        val intent = Intent(context, LoginActivity::class.java)
                        intent.flags =
                            Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                        context.startActivity(intent)
                        (context as? MainActivity)?.finish()
                    }
                ) {
                    Text(
                        text = "Confirm",
                        color = Color(0xFF07c160),
                        fontWeight = FontWeight.Medium
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showLogoutDialog = false }
                ) {
                    Text(
                        text = "Cancel",
                        color = Color(0xFF666666)
                    )
                }
            }
        )
    }

    // 设置对话框
    if (showSettingsDialog) {
        SettingsDialog(
            fontSize = fontSize,
            onFontSizeChange = {
                fontSize = it
                settingsManager.fontSize = it
            },
            isDarkTheme = isDarkTheme,
            onThemeChange = { newTheme ->
                onThemeChange(newTheme)
                settingsManager.isDarkTheme = newTheme
            },
            highlightColor = highlightColor,
            onHighlightColorClick = {
                showColorPickerDialog = true
            },
            primaryLanguage = primaryLanguage,
            onPrimaryLanguageChange = {
                primaryLanguage = it
                settingsManager.primaryLanguage = it
            },
            secondaryLanguage = secondaryLanguage,
            onSecondaryLanguageChange = {
                secondaryLanguage = it
                settingsManager.secondaryLanguage = it
            },
            primaryServiceType = primaryServiceType,
            onPrimaryServiceTypeChange = {
                primaryServiceType = it
                settingsManager.primaryVoiceService = it
            },
            secondaryServiceType = secondaryServiceType,
            onSecondaryServiceTypeChange = {
                secondaryServiceType = it
                settingsManager.secondaryVoiceService = it
            },
            // 第三语言参数
            tertiaryLanguage = tertiaryLanguage,
            onTertiaryLanguageChange = {
                tertiaryLanguage = it
                settingsManager.tertiaryLanguage = it
            },
            tertiaryServiceType = tertiaryServiceType,
            onTertiaryServiceTypeChange = {
                tertiaryServiceType = it
                settingsManager.tertiaryServiceType = it
            },
            isAutoLanguageEnabled = isAutoLanguageEnabled,
            onAutoLanguageEnabledChange = {
                isAutoLanguageEnabled = it
                settingsManager.isAutoLanguageEnabled = it
            },
            onDismiss = { showSettingsDialog = false }
        )
    }

    // 词汇表对话框
    if (showVocabularyDialog) {
        VocabularyListDialog(
            onDismiss = { showVocabularyDialog = false },
            onVocabularySelected = { sceneIds ->
                // 当选择词汇表时，调用词汇表管理器加载数据
                coroutineScope.launch {
                    val success = glossaryManager.loadGlossaryBySceneIds(sceneIds)
                    if (success) {
                        println("词汇表加载成功，共 ${glossaryManager.getGlossaryCount()} 条记录")
                    } else {
                        println("词汇表加载失败: ${glossaryManager.errorMessage.value}")
                    }
                }
            }
        )
    }

    // 替换表对话框
    if (showReplaceSceneDialog) {
        ReplaceSceneListDialog(
            onDismiss = { showReplaceSceneDialog = false },
            onReplaceSceneSelected = { replaceScene ->
                if (replaceScene.id == -1) {
                    // 清空替换表选择
                    Log.d("MainActivity", "清空替换表选择")
                    replaceManager.clearReplace()
                } else {
                    // 当选择替换表场景时，调用接口获取替换表数据并暂存
                    Log.d("MainActivity", "选择替换表: ${replaceScene.name}, ID: ${replaceScene.id}")
                    coroutineScope.launch {
                        Log.d("MainActivity", "开始加载替换表数据...")
                        val success = replaceManager.loadReplacesBySceneId(replaceScene.id)
                        if (success) {
                            Log.d("MainActivity", "替换表加载成功: ${replaceScene.name}, 共 ${replaceManager.getReplaceCount()} 条记录")
                        } else {
                            Log.e("MainActivity", "替换表加载失败: ${replaceManager.errorMessage.value}")
                        }
                    }
                }
            }
        )
    }

    // 颜色选择器对话框
    if (showColorPickerDialog) {
        com.greenterp.ui.components.ColorPickerDialog(
            currentColor = highlightColor,
            onColorSelected = { selectedColor ->
                highlightColor = selectedColor
                settingsManager.highlightColor = selectedColor.toArgb()
            },
            onDismiss = { showColorPickerDialog = false },
            isDarkTheme = isDarkTheme,
            title = "Choose Highlight Color"
        )
    }
}




@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    TerpMetaAndroidTheme {
        MainScreen()
    }
}






